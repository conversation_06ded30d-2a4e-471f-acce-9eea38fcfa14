"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = __importDefault(require("./app"));
const config_1 = require("@/config");
const database_1 = require("@/config/database");
const logger_1 = require("@/utils/logger");
const PORT = config_1.config.port;
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
const gracefulShutdown = async (signal) => {
    logger_1.logger.info(`Received ${signal}. Starting graceful shutdown...`);
    try {
        await (0, database_1.disconnectDatabase)();
        logger_1.logger.info('Database disconnected successfully');
        process.exit(0);
    }
    catch (error) {
        logger_1.logger.error('Error during graceful shutdown:', error);
        process.exit(1);
    }
};
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
const startServer = async () => {
    try {
        await (0, database_1.connectDatabase)();
        const server = app_1.default.listen(PORT, '0.0.0.0', () => {
            logger_1.logger.info(`🚀 Cymatics Backend Server is running on port ${PORT}`);
            logger_1.logger.info(`📊 Environment: ${config_1.config.env}`);
            logger_1.logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
            logger_1.logger.info(`📚 API docs: http://localhost:${PORT}/api`);
            logger_1.logger.info(`🌐 Network access: http://0.0.0.0:${PORT}`);
        });
        server.on('error', (error) => {
            if (error.syscall !== 'listen') {
                throw error;
            }
            const bind = typeof PORT === 'string' ? `Pipe ${PORT}` : `Port ${PORT}`;
            switch (error.code) {
                case 'EACCES':
                    logger_1.logger.error(`${bind} requires elevated privileges`);
                    process.exit(1);
                    break;
                case 'EADDRINUSE':
                    logger_1.logger.error(`${bind} is already in use`);
                    process.exit(1);
                    break;
                default:
                    throw error;
            }
        });
        return server;
    }
    catch (error) {
        logger_1.logger.error('Failed to start server:', error);
        process.exit(1);
    }
};
startServer();
//# sourceMappingURL=server.js.map