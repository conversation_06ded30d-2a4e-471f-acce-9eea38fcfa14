{"version": 3, "file": "InferTypesFromRule.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/InferTypesFromRule.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAEnE;;GAEG;AACH,KAAK,wBAAwB,CAAC,CAAC,IAC7B,CAAC,SAAS,UAAU,CAAC,MAAM,YAAY,EAAE,MAAM,QAAQ,CAAC,GACpD,QAAQ,GACR,CAAC,SAAS,kBAAkB,CAAC,MAAM,YAAY,EAAE,MAAM,QAAQ,CAAC,GAC9D,QAAQ,GACR,OAAO,CAAC;AAEhB;;GAEG;AACH,KAAK,2BAA2B,CAAC,CAAC,IAChC,CAAC,SAAS,UAAU,CAAC,MAAM,WAAW,EAAE,MAAM,SAAS,CAAC,GACpD,WAAW,GACX,CAAC,SAAS,kBAAkB,CAAC,MAAM,WAAW,EAAE,MAAM,SAAS,CAAC,GAC9D,WAAW,GACX,OAAO,CAAC;AAEhB,OAAO,EAAE,wBAAwB,EAAE,2BAA2B,EAAE,CAAC"}